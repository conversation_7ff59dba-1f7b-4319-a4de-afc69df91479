// 查询 jobdb.richemont_onboarding_customize_email_task 表中的数据 email_type = vca的， 根据xxl jobId 调用xxl服务端移除任务
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    "host": "db-d.dqprism.com",
    "user": "daqi",
    "password": "bz8YdeGro27erJ7kH4jFEeay7h8qeicI",
    "database": "jobdb",
    "port": 3306
};

// XXL-Job 配置
const xxlJobConfig = {
    adminUrl: 'http://schedulerx-t.dqprism.com:18082/xxl-job-admin', // 请替换为实际的XXL-Job管理地址
    accessToken: '5008f0d4df839a0c2ceab61a6b9a22b1'
};

/**
 * 创建数据库连接
 */
async function createConnection() {
    const connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功');
    return connection;
}

/**
 * 查询VCA类型的邮件任务
 */
async function getVCAEmailTasks(connection) {
    const query = `
        SELECT id, job_id, email_type
        FROM jobdb.richemont_onboarding_customize_email_task
        WHERE email_type = 'vca'
    `;

    const [rows] = await connection.execute(query);
    console.log(`找到 ${rows.length} 个VCA类型的邮件任务`);
    return rows;
}

/**
 * 调用XXL-Job API删除任务
 */
async function removeXxlJob(jobId) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    const response = await fetch(
        `${xxlJobConfig.adminUrl}/jobinfo/removeJob`,
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json; charset=UTF-8',
                'XXL-JOB-ACCESS-TOKEN': xxlJobConfig.accessToken,
                'CUSTOM-TRACE-ID': require('uuid').v4()
            },
            body: JSON.stringify({id: jobId}),
            signal: controller.signal
        }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data && data.code === 200) {
        console.log(`✅ 成功删除XXL-Job任务: ${jobId}`);
        return {success: true, jobId};
    } else {
        throw new Error(`❌ 删除XXL-Job任务失败: ${jobId}, 响应:`, data);
    }
}

/**
 * 更新数据库中的任务状态（可选：将xxl_job_id设为null）
 */
async function deleteDbRow(connection, taskId) {
    // 如果删除成功，可以选择将xxl_job_id删除
    const updateQuery = `
        delete
        from jobdb.richemont_onboarding_customize_email_task
        WHERE id = ?
    `;
    await connection.execute(updateQuery, [taskId]);
    console.log(`✅ 更新任务状态成功: ${taskId}`);
}

/**
 * 批量删除VCA邮件任务
 */
async function batchDeleteVCAJobs() {
    let connection;

    try {
        console.log('🚀 开始批量删除VCA邮件任务...');

        // 创建数据库连接
        connection = await createConnection();

        // 查询VCA类型的邮件任务
        const vcaTasks = await getVCAEmailTasks(connection);

        if (vcaTasks.length === 0) {
            console.log('📝 没有找到需要删除的VCA邮件任务');
            return;
        }

        console.log(`📋 准备删除 ${vcaTasks.length} 个XXL-Job任务`);

        // 统计结果
        const results = {
            total: vcaTasks.length,
            success: 0,
            failed: 0,
            errors: []
        };

        // 批量删除（可以考虑并发控制）
        for (const task of vcaTasks) {
            console.log(`\n🔄 正在处理任务 ID: ${task.id}, XXL-Job ID: ${task.xxl_job_id}`);

            const result = await removeXxlJob(task.xxl_job_id);

            if (result.success) {
                results.success++;
                // 更新数据库状态
                await deleteDbRow(connection, task.id);
            } else {
                results.failed++;
                results.errors.push({
                    taskId: task.id,
                    xxlJobId: task.job_id,
                    error: result.error
                });
            }

            // 添加延迟避免请求过于频繁
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        // 输出最终结果
        console.log('\n📊 批量删除结果统计:');
        console.log(`总任务数: ${results.total}`);
        console.log(`成功删除: ${results.success}`);
        console.log(`删除失败: ${results.failed}`);

        if (results.errors.length > 0) {
            console.log('\n❌ 失败的任务详情:');
            results.errors.forEach(error => {
                console.log(`  任务ID: ${error.taskId}, XXL-Job ID: ${error.xxlJobId}, 错误: ${error.error}`);
            });
        }

        console.log('\n✅ 批量删除任务完成');

    } catch (error) {
        console.error('❌ 批量删除过程中发生错误:', error);
    } finally {
        // 关闭数据库连接
        if (connection) {
            await connection.end();
            console.log('📝 数据库连接已关闭');
        }
    }
}

/**
 * 主函数
 */
async function main() {
    try {
        // 检查配置
        if (xxlJobConfig.adminUrl.includes('your-xxl-job-admin-address') ||
            xxlJobConfig.accessToken.includes('your-access-token')) {
            console.error('❌ 请先配置正确的XXL-Job管理地址和访问令牌');
            process.exit(1);
        }

        await batchDeleteVCAJobs();
    } catch (error) {
        console.error('❌ 程序执行失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
main().catch(e => {
    console.error('❌ 异步操作发生错误:', e);
});


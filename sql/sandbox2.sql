select *
from logdb.log_sms_send_record_253
order by id desc;

select *
from jobdb.job_application
where id = 4024380;

select *
from jobdb.job_application
where candidate_profile_id = '6751423e87209b5302bbc9e0';

select *
from jobdb.job_application_duplicate_info
where candidate_profile_id = '6751423e87209b5302bbc9e0';
# 4022405
select *
from jobdb.job_application_duplicate_info
where application_id = '4024380';

select *
from jobdb.job_position
where id = 20438100;
SELECT UNIX_TIMESTAMP() * 1000;


insert into userdb.user_hr_permission
values ();


select a.company_id, e.name, a.name
FROM hrdb.hr_ats_process_company a
         left join hrdb.hr_ats_process_company_item b on a.id = b.parent_id
         left join hrdb.hr_ats_phase_base_item c on c.id = b.item_id
         left join hrdb.hr_ats_phase_base d on d.id = c.parent_id
         left join hrdb.hr_company e on a.company_id = e.id
where d.name = '面试型'
  and a.disable = 0
  and b.disable = 0
  and c.disable = 0
group by a.id
having count(a.id) > 1
;


select *
from hrdb.hr_company_interview a
         left join jobdb.job_application_ats_process b on a.app_id = b.app_id
where company_id = 5164899
group by b.process_id, a.interview_round
having count(*) > 1
;

select *
from hrdb.hr_company_interview a
where company_id = 5164899
  and app_id = 14214364;

select *
from hrdb.hr_ats_process_company_item
where parent_id = 98
;


select *
from hrdb.hr_custome_offer
where application_id = 4033023
  and broad_type = 'offer'
  and deleted = 0;


select *
from messagedb.ats_process_notice_custom_field_dict
where ats_process_notice_custom_field_dict.id in (
                                                  12,
                                                  43,
                                                  44
    );


select *
from messagedb.ats_process_notice_template_custom_field
where template_type_id = 21
;



INSERT INTO messagedb.ats_process_notice_custom_field_dict (id, title, en_title)
VALUES (83, '合同链接有效期', null);
INSERT INTO messagedb.ats_process_notice_custom_field_dict (id, title, en_title)
VALUES (84, '合同确认链接', null);


INSERT INTO messagedb.ats_process_notice_template_custom_field (template_type_id, template_type_sub_id, field_dict_id)
VALUES (81, null, 12);
INSERT INTO messagedb.ats_process_notice_template_custom_field (template_type_id, template_type_sub_id, field_dict_id)
VALUES (81, null, 83);
INSERT INTO messagedb.ats_process_notice_template_custom_field (template_type_id, template_type_sub_id, field_dict_id)
VALUES (81, null, 84);

select *
from hrdb.hr_offer_template
where id = 305;


select b.*
from jobdb.job_application a
         left join jobdb.job_position b on a.position_id = b.id
where a.id = 4033030;


select *
from hrdb.hr_custome_offer
where application_id = 4033167;

select *
from hrdb.hr_custome_offer
where application_id = 4033217
  and deleted = 0
  and broad_type = 'contract';



select *
from jobdb.job_application_employee_info
where application_id = 4033307;



SELECT *
FROM hrdb.hr_custome_offer
WHERE application_id = 4033327
  and broad_type = 'contract';
SELECT *
FROM hrdb.hr_custom_offer_delay_task
WHERE hr_custom_offer_id in
      (SELECT id FROM hrdb.hr_custome_offer WHERE application_id = 4033327 and broad_type = 'contract');



SELECT *
FROM hrdb.hr_custome_offer
WHERE application_id = 4033608
  and broad_type = 'contract';
SELECT *
FROM hrdb.hr_custom_offer_delay_task
WHERE hr_custom_offer_id = 4220;


select *
from hrdb.hr_custome_offer a
         left join hrdb.hr_custom_offer_delay_task b
                   on a.id = b.hr_custom_offer_id
WHERE application_id = 4033640
  and broad_type = 'contract';


select id, company_id
from jobdb.job_application
where id = 4033621;



select *
from messagedb.ats_process_notice_email_template t
where template_id = 1121;

select *
from messagedb.ats_process_notice_email_template t
where title = 'offer审批通知';


select *
from jobdb.job_application_employee_info
where application_id = 4033699;

-- <EMAIL>
select *
from messagedb.message_company_exclude
where company_id = 3211002;
select *
from messagedb.message_company_exclude
where company_id = 3276354;


-- <EMAIL>
-- <EMAIL>
-- <EMAIL>


select *
from jobdb.job_application
where id = 4033790;


select *
from hrdb.hr_custome_offer_template
where company_id = 2989729;


select *
from userdb.user_hr_account
where id = 1005297;


select *
from employeedb.richemont_employee
where personnel_number = '194911';
select *
from employeedb.richemont_employee
where personnel_number = '170535';
select *
from employeedb.richemont_employee
where personnel_number = '214341';
select *
from employeedb.richemont_employee
where personnel_number = '194911';
select *
from employeedb.richemont_employee
where personnel_number = '235136';
select *
from employeedb.richemont_employee
where application_id = '4035416';

select *
from jobdb.job_application_employee_info
where application_id = 4035416;

select *
from employeedb.richemont_employee
where email = '<EMAIL>';
select *
from employeedb.richemont_employee
where email = '<EMAIL>';
select *
from employeedb.richemont_employee
where email = '<EMAIL>';
select *
from employeedb.richemont_employee
where email = '<EMAIL>';
select *
from employeedb.richemont_employee
where email = '<EMAIL>';

select *
from jobdb.context_store
where uid = 'n0196859d927b778fb9a5154f8d6c0993';


select position_id
from jobdb.job_application
where id = 4035433;

select publisher
from jobdb.job_position
where id = ********;

select email
from userdb.user_hr_account
where id = 1005323;

select *
from employeedb.richemont_employee
where email = '<EMAIL>';



select *
from userdb.user_hr_account
where email = '<EMAIL>';


select *
from jobdb.richemont_onboarding_customize_email_task
where email_type = 'VCA'
  and create_time > '2025-05-07 00:00:00';

select *
from jobdb.richemont_onboarding_customize_email_task
where email_type = 'VCA'
  and create_time > '2025-05-07 00:00:00'
  and cron like '%2025%';


update messagedb.ats_process_notice_email_template
set html = '<p>亲爱的#HR姓名#，您好：</p>
<p>&nbsp;</p>
<p>「#猎头公司#」的「#猎头姓名#」上传了一个新的简历推荐到「#职位名称#」：</p>
<p>&nbsp;</p>
<p>候选人姓名：#求职者姓名#</p>
<p>推荐理由：</p>
<p>#被推荐原因#</p>
<p>候选人详情链接：</p>
<p>#链接#</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
#RichemontHR#
<p>(系统邮件，请勿直接回复)</p>'

where template_id = 275
;



select *
from logdb.log_sendcloud_email_send_result
where unique_identifier = '461115'
  and business_source = 'ONBOARDING_WARM_UP';


select *
from jobdb.job_application
where id = 4035477;


select *
from jobdb.richemont_onboarding_employee_warmup_status
where employee_number = '235136';


select
       *
from jobdb.richemont_onboarding_employee_warmup_trace
WHERE employee_number = '235136' and company_id = 3276354 and deleted = 0;


select
       *
from jobdb.richemont_onboarding_employee_warmup_email_trace
WHERE ( company_id = 3276354 and deleted = 0 );


SELECT count(0) FROM jobdb.richemont_onboarding_employee_warmup_trace WHERE (deleted = 0 AND company_id = ? AND work_flow_id = ?) ;


delete from jobdb.richemont_onboarding_employee_warmup_email_trace
where create_time < '2025-05-22 12:11:52';


-- 235136
-- 239025


select *
from employeedb.richemont_employee
where personnel_number='239025';
